"""
PowScan Dataset

Author: Your Name
Please cite our work if the code is helpful to you.
"""

import os
import numpy as np
from .defaults import DefaultDataset
from .builder import DATASETS
from .powscan_constants import CLASS_NAMES, CLASS2ID, CLASS_IDS, UNLABELED_ID, IGNORE_INDEX, update_constants
from pointcept.utils.cache import shared_dict


@DATASETS.register_module()
class PowScanDataset(DefaultDataset):
    # 最大点数限制
    MAX_POINTS_PER_SAMPLE = 120000
    # 网格重叠比例
    GRID_OVERLAP_RATIO = 0.05
    
    def __init__(
        self,
        split="train",
        data_root="data/powscan",
        transform=None,
        test_mode=False,
        test_cfg=None,
        cache=False,
        ignore_index=IGNORE_INDEX,
        loop=1,
    ):
        # 更新全局常量
        update_constants(data_root)
        
        super(PowScanDataset, self).__init__(
            split=split,
            data_root=data_root,
            transform=transform,
            test_mode=test_mode,
            test_cfg=test_cfg,
            cache=cache,
            ignore_index=ignore_index,
            loop=loop,
        )
        
        # 设置有效的资产类型
        self.VALID_ASSETS = ["coord", "strength", "segment"]

    def get_data_list(self):
        """获取数据列表，支持大文件拆分"""
        if isinstance(self.split, str):
            split_file = os.path.join(self.data_root, self.split + ".txt")
            if os.path.exists(split_file):
                with open(split_file, 'r') as f:
                    original_data_list = [line.strip() for line in f.readlines()]
            else:
                # 如果没有分割文件，则使用所有处理后的数据
                original_data_list = []
                processed_dir = os.path.join(self.data_root, "processed")
                if os.path.exists(processed_dir):
                    for scene in os.listdir(processed_dir):
                        scene_dir = os.path.join(processed_dir, scene)
                        if os.path.isdir(scene_dir):
                            for fragment in os.listdir(scene_dir):
                                if fragment.endswith(".npy"):
                                    # 移除.npy扩展名
                                    fragment_name = fragment[:-4]
                                    original_data_list.append(os.path.join(scene_dir, fragment_name))
        else:
            raise NotImplementedError("PowScan dataset only supports string split")
        
        # 扩展数据列表以包含所有拆分样本
        expanded_data_list = []
        for data_path in original_data_list:
            # 检查文件是否需要拆分
            coord_path = data_path + ".npy"
            if os.path.exists(coord_path):
                data = np.load(coord_path)
                if data.shape[0] > self.MAX_POINTS_PER_SAMPLE:
                    # 对于大文件，添加多个虚拟路径（每个拆分一个）
                    coords = data[:, :3]
                    min_coord = np.min(coords, axis=0)
                    max_coord = np.max(coords, axis=0)
                    spatial_extent = max_coord - min_coord
                    
                    grid_count = max(2, int(np.ceil(data.shape[0] / self.MAX_POINTS_PER_SAMPLE)))
                    grid_dims = self._calculate_grid_dimensions(grid_count, spatial_extent)
                    
                    for i in range(grid_dims[0]):
                        for j in range(grid_dims[1]):
                            for k in range(grid_dims[2]):
                                # 为每个网格创建虚拟路径
                                virtual_path = f"{data_path}_grid_{i}_{j}_{k}"
                                expanded_data_list.append(virtual_path)
                else:
                    # 小文件直接添加
                    expanded_data_list.append(data_path)
            else:
                # 文件不存在，直接添加原始路径
                expanded_data_list.append(data_path)
        
        return expanded_data_list

    def get_data(self, idx):
        """获取单个数据样本，支持大文件拆分和虚拟路径"""
        data_path = self.data_list[idx % len(self.data_list)]
        name = self.get_data_name(idx)
        
        if self.cache:
            cache_name = f"pointcept-{name}"
            return shared_dict(cache_name)

        # 检查是否为虚拟路径（包含_grid_）
        if "_grid_" in data_path:
            # 虚拟路径，需要从原始文件加载并提取指定网格
            # 提取原始文件路径和网格坐标
            base_path = data_path.rsplit("_grid_", 1)[0]
            grid_coords = data_path.split("_grid_")[1].split("_")
            i, j, k = map(int, grid_coords)
            
            # 加载原始数据
            original_data_dict = self._load_original_data(base_path)
            
            # 直接提取指定网格，避免全部分拆
            segment_dict = self._extract_single_grid(original_data_dict, i, j, k)
            return segment_dict
        else:
            # 普通路径，直接加载
            data_dict = self._load_original_data(data_path)
            
            # 检查点数并决定是否拆分（用于缓存模式）
            if data_dict["coord"].shape[0] > self.MAX_POINTS_PER_SAMPLE:
                data_dicts = self._split_by_coordinate_grid(data_dict)
                # 返回第一个拆分样本
                return data_dicts[0]
            else:
                return data_dict

    def _load_original_data(self, data_path):
        """加载原始数据而不进行拆分"""
        data_dict = {}
        
        # 加载坐标、颜色和标签
        coord_path = data_path + ".npy"
        if os.path.exists(coord_path):
            data = np.load(coord_path)
            data_dict["coord"] = data[:, :3].astype(np.float32)
            
            # 自动识别数据格式版本
            num_cols = data.shape[1]
            if num_cols == 7:  # 原始格式: xyz(3) + rgb(3) + label(1)
                data_dict["color"] = data[:, 3:6].astype(np.float32)
                data_dict["segment"] = data[:, 6].astype(np.int32)
            elif num_cols == 8:  # 增强格式: xyz(3) + strength(1) + rgb(3) + label(1)
                data_dict["strength"] = data[:, 3:4].astype(np.float32)
                data_dict["color"] = data[:, 4:7].astype(np.float32)
                data_dict["segment"] = data[:, 7].astype(np.int32)
            elif num_cols == 5:  # 纯强度格式: xyz(3) + strength(1) + label(1)
                data_dict["strength"] = data[:, 3:4].astype(np.float32)
                data_dict["segment"] = data[:, 4].astype(np.int32)
            else:
                raise ValueError(f"Unsupported data format with {num_cols} columns")
        else:
            raise FileNotFoundError(f"Data file not found: {coord_path}")

        data_dict["name"] = os.path.basename(data_path)
        
        # 确保数据类型正确
        if "coord" in data_dict.keys():
            data_dict["coord"] = data_dict["coord"].astype(np.float32)
            
        if "strength" in data_dict.keys():
            data_dict["strength"] = data_dict["strength"].astype(np.float32)
            
        if "segment" in data_dict.keys():
            data_dict["segment"] = data_dict["segment"].reshape([-1]).astype(np.int32)
            # 确保标签ID与class_mapping.json一致
            # 未定义的标签映射为ignore_index
            if CLASS_IDS:  # 只有在有定义类别时才检查
                unique_labels = np.unique(data_dict["segment"])
                for label in unique_labels:
                    if label not in CLASS_IDS:
                        # 将未定义的标签映射为ignore_index
                        mask = data_dict["segment"] == label
                        data_dict["segment"][mask] = self.ignore_index
        else:
            data_dict["segment"] = (
                np.ones(data_dict["coord"].shape[0], dtype=np.int32) * self.ignore_index
            )
            
        return data_dict

    def _split_by_coordinate_grid(self, data_dict, min_points=120000):
        """基于坐标网格拆分大点云文件"""
        coords = data_dict["coord"]
        points_count = coords.shape[0]
        
        # 计算边界框和空间范围
        min_coord = np.min(coords, axis=0)
        max_coord = np.max(coords, axis=0)
        spatial_extent = max_coord - min_coord
        
        # 计算需要的网格数量
        grid_count = max(2, int(np.ceil(points_count / min_points)))
        # 确保网格在三个维度上均匀分布
        grid_dims = self._calculate_grid_dimensions(grid_count, spatial_extent)
        
        segments = []
        for i in range(grid_dims[0]):
            for j in range(grid_dims[1]):
                for k in range(grid_dims[2]):
                    # 获取网格边界
                    boundaries = self._get_grid_boundaries(min_coord, spatial_extent, grid_dims, i, j, k)
                    
                    # 选择在当前网格内的点
                    mask = (
                        (coords[:, 0] >= boundaries["start_x"]) & (coords[:, 0] < boundaries["end_x"]) &
                        (coords[:, 1] >= boundaries["start_y"]) & (coords[:, 1] < boundaries["end_y"]) &
                        (coords[:, 2] >= boundaries["start_z"]) & (coords[:, 2] < boundaries["end_z"])
                    )
                    
                    # 确保分割后的样本包含足够的点
                    if np.sum(mask) >= min_points:
                        segment_dict = self._create_segment(data_dict, mask, i, j, k)
                        segments.append(segment_dict)
        
        # 如果没有生成任何满足条件的分割，则返回原始数据
        if len(segments) == 0:
            segments.append(data_dict)
        
        return segments

    def _calculate_grid_dimensions(self, grid_count, spatial_extent):
        return [1, grid_count, 1]
        """计算网格在三个维度上的分布"""
        # 根据空间范围的比例分配网格维度
        total_extent = np.sum(spatial_extent)
        x_ratio = spatial_extent[0] / total_extent
        y_ratio = spatial_extent[1] / total_extent
        z_ratio = spatial_extent[2] / total_extent
        
        x_dim = max(1, round(grid_count * x_ratio))
        y_dim = max(1, round(grid_count * y_ratio))
        z_dim = max(1, grid_count - x_dim - y_dim)
        
        return [x_dim, y_dim, z_dim]

    def _extract_single_grid(self, data_dict, i, j, k):
        """提取单个网格样本，避免全部分拆"""
        coords = data_dict["coord"]
        points_count = coords.shape[0]
        
        # 计算边界框和空间范围
        min_coord = np.min(coords, axis=0)
        max_coord = np.max(coords, axis=0)
        spatial_extent = max_coord - min_coord
        
        # 计算网格数量（与get_data_list中一致）
        grid_count = max(2, int(np.ceil(points_count / self.MAX_POINTS_PER_SAMPLE)))
        grid_dims = self._calculate_grid_dimensions(grid_count, spatial_extent)
        
        # 确保网格坐标在范围内
        if i >= grid_dims[0] or j >= grid_dims[1] or k >= grid_dims[2]:
            # 如果坐标超出范围，返回第一个网格
            i, j, k = 0, 0, 0
        
        # 获取网格边界
        boundaries = self._get_grid_boundaries(min_coord, spatial_extent, grid_dims, i, j, k)
        
        # 选择在当前网格内的点
        mask = (
            (coords[:, 0] >= boundaries["start_x"]) & (coords[:, 0] < boundaries["end_x"]) &
            (coords[:, 1] >= boundaries["start_y"]) & (coords[:, 1] < boundaries["end_y"]) &
            (coords[:, 2] >= boundaries["start_z"]) & (coords[:, 2] < boundaries["end_z"])
        )
        
        # 使用_create_segment创建分段样本以确保一致性
        segment_dict = self._create_segment(data_dict, mask, i, j, k)
        return segment_dict

    def _create_segment(self, data_dict, mask, i, j, k):
        """从原始数据创建分段样本"""
        segment_dict = {}
        for key in data_dict:
            if key not in ["name", "split"] and hasattr(data_dict[key], '__len__'):
                segment_dict[key] = data_dict[key][mask].copy()
            else:
                segment_dict[key] = data_dict[key]
        
        # 添加网格信息到名称
        segment_dict["name"] = f"{data_dict['name']}_grid_{i}_{j}_{k}"
        return segment_dict

    def _get_grid_boundaries(self, min_coord, spatial_extent, grid_dims, i, j, k):
        """计算指定网格的边界（带重叠）"""
        cell_size = spatial_extent / grid_dims
        
        start_x = min_coord[0] + (i * cell_size[0]) - (self.GRID_OVERLAP_RATIO * cell_size[0])
        end_x = min_coord[0] + ((i + 1) * cell_size[0]) + (self.GRID_OVERLAP_RATIO * cell_size[0])
        
        start_y = min_coord[1] + (j * cell_size[1]) - (self.GRID_OVERLAP_RATIO * cell_size[1])
        end_y = min_coord[1] + ((j + 1) * cell_size[1]) + (self.GRID_OVERLAP_RATIO * cell_size[1])
        
        start_z = min_coord[2] + (k * cell_size[2]) - (self.GRID_OVERLAP_RATIO * cell_size[2])
        end_z = min_coord[2] + ((k + 1) * cell_size[2]) + (self.GRID_OVERLAP_RATIO * cell_size[2])
        
        return {
            "start_x": start_x,
            "end_x": end_x,
            "start_y": start_y,
            "end_y": end_y,
            "start_z": start_z,
            "end_z": end_z
        }

    def get_data_name(self, idx):
        """获取数据名称"""
        data_path = self.data_list[idx % len(self.data_list)]
        # 从路径中提取场景名和片段名
        path_parts = data_path.split(os.sep)
        if len(path_parts) >= 2:
            scene_name = path_parts[-2]
            fragment_name = path_parts[-1]
            return f"{scene_name}-{fragment_name}"
        return os.path.basename(data_path)